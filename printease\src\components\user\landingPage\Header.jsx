import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'

function Header() {
  const [isScrolled, setIsScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <header className="fixed w-full top-0 z-50 bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200/50 transition-all duration-300">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo */}
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-xl">P</span>
                </div>
                <h1 className="text-2xl font-bold text-gray-900 transition-colors duration-300">
                  PrintEase
                </h1>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="hidden md:flex space-x-8">
            <a href="#home" className="px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 hover:bg-gray-100 text-gray-700 hover:text-blue-600">
              Home
            </a>
            <a href="#about" className="px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 hover:bg-gray-100 text-gray-700 hover:text-blue-600">
              About Us
            </a>
            <a href="#contact" className="px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 hover:bg-gray-100 text-gray-700 hover:text-blue-600">
              Contact Us
            </a>
          </nav>

          {/* Login Button */}
          <div className="flex items-center">
            <Link 
              to="/login"
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-xl text-sm font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
            >
              Sign In
            </Link>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
