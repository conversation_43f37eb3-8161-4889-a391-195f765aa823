@import "tailwindcss";

/* Global Font Settings */
* {
  font-family: 'Plus Jakarta Sans', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Display fonts for headings */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Outfit', 'Plus Jakarta Sans', system-ui, sans-serif;
  font-weight: 800;
  letter-spacing: -0.025em;
}

/* Enhanced typography */
.font-display {
  font-family: 'Outfit', 'Plus Jakarta Sans', system-ui, sans-serif;
  font-weight: 800;
  letter-spacing: -0.025em;
}

.font-body {
  font-family: 'Plus Jakarta Sans', system-ui, sans-serif;
  font-weight: 600;
  line-height: 1.6;
}

/* Custom animations */
@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-scroll {
  animation: scroll 30s linear infinite;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Better text rendering */
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}
